package com.snap.composer.attributes.impl.richtext

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.text.Layout
import android.text.TextPaint
import android.text.style.AlignmentSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.MetricAffectingSpan
import android.text.style.StrikethroughSpan
import android.text.style.UnderlineSpan
import android.util.TypedValue
import com.snap.composer.attributes.impl.fonts.FontDescriptor
import com.snap.composer.attributes.impl.fonts.FontManager
import com.snap.composer.attributes.impl.fonts.MissingFontsTracker
import com.snap.composer.exceptions.AttributeError

class CustomTypefaceSpan(val typeface: Typeface) : MetricAffectingSpan() {
    override fun updateDrawState(tp: TextPaint) {
        tp.typeface = typeface
    }

    override fun updateMeasureState(tp: TextPaint) {
        tp.typeface = typeface
    }
}

data class FontAttributes(
    var textDecoration: TextDecoration?,
    var fontSize: Float,
    var fontName: String?,
    var lineHeight: Float?,
    var numberOfLines: Int?,
    var letterSpacing: Float?,
    var adjustsFontSizeToFitWidth: Boolean?,
    var minimumScaleFactor: Float?,
    var color: Int,
    var alignment: TextAlignment,
    var isUnscaled: Boolean = false,
    var outlineColor: Int? = null,
    var outlineWidth: Float
) {

    companion object {
        val default = FontAttributes(
            null,
            12f,
            null,
            null,
            null,
            null,
            null,
            null,
            Color.BLACK,
            TextAlignment.LEFT,
            false,
            null,
            0F)
        val buttonDefault = FontAttributes(
            null,
            12f,
            null,
            null,
            null,
            null,
            null,
            null,
            Color.BLACK,
            TextAlignment.CENTER,
            false,
            null,
            0F
        )
        private const val PX_SUFFIX = "px"
        private const val PT_SUFFIX = "pt"
        private const val POSITION_OF_DYNAMIC_TYPE = 3
    }

    fun enumerateSpans(fontManager: FontManager,
                       missingFontsTracker: MissingFontsTracker,
                       disableTextReplacement: Boolean = false,
                       closure: (Any) -> Unit) {
        closure(TextSizeSpan(resolveFontSize(fontManager.context)))

        closure(AlignmentSpan.Standard(when (alignment) {
            TextAlignment.LEFT -> Layout.Alignment.ALIGN_NORMAL
            TextAlignment.CENTER -> Layout.Alignment.ALIGN_CENTER
            TextAlignment.RIGHT -> Layout.Alignment.ALIGN_OPPOSITE
            else -> Layout.Alignment.ALIGN_NORMAL
        }))

        if (textDecoration != null) {
            when (textDecoration!!) {
                TextDecoration.UNDERLINE -> closure(UnderlineSpan())
                TextDecoration.STRIKETHROUGH -> closure(StrikethroughSpan())
                TextDecoration.NONE -> {}
            }
        }

        val typeface = resolveTypeface(fontManager, missingFontsTracker)
        if (typeface != null) {
            closure(CustomTypefaceSpan(typeface))
        }

        // forceOutline renders transparent text for non-outlined text, and gets us only the outlines
        // this is then overriden in onDraw for ComposerEditText to overlay on top of text\
        if (!disableTextReplacement && outlineColor != null && outlineWidth > 0) {
            closure(OutlineReplacementSpan(color, outlineColor!!, outlineWidth))
        } else {
            closure(ForegroundColorSpan(color))
        }
    }

    fun applyFont(font: String) {
        // We scale all fonts by default, unless specified otherwise in the font's 3rd parameter
        isUnscaled = false

        when (font.lowercase()) {
            "title1" -> {
                fontName = "title1"
                fontSize = 25f
            }
            "title2" -> {
                fontName = "title2"
                fontSize = 19f
            }
            "title3" -> {
                fontName = "title3"
                fontSize = 17f
            }
            "body" -> {
                fontName = "body"
                fontSize = 14f
            }
            else -> {
                val fontPieces = font.split(" ")
                fontName = fontPieces[0]
                if (fontPieces.size > 1) {
                    try {
                        fontSize = fontPieces[1]
                                .removeSuffix(PX_SUFFIX)
                                .removeSuffix(PT_SUFFIX)
                               .toFloat()
                    } catch (e: NumberFormatException) {
                        throw AttributeError("Found ${fontPieces[1]}, expected float for font size")
                    }

                    // The special "unscaled" type can be used to disable text scaling
                    if (fontPieces.size >= POSITION_OF_DYNAMIC_TYPE && fontPieces[2].lowercase() == "unscaled") {
                        isUnscaled = true
                    }
                }
            }
        }
    }

    fun applyTextDecoration(attributeVal: String) {
        textDecoration = when (attributeVal) {
            "underline" -> TextDecoration.UNDERLINE
            "strikethrough" -> TextDecoration.STRIKETHROUGH
            else -> TextDecoration.NONE
        }
    }

    fun applyTextAlign(attributeVal: String) {
        alignment = when (attributeVal) {
            "center" -> TextAlignment.CENTER
            "right" -> TextAlignment.RIGHT
            "justified" -> TextAlignment.JUSTIFIED
            else -> TextAlignment.LEFT
        }
    }

    fun resolveTypeface(fontManager: FontManager, missingFontsTracker: MissingFontsTracker): Typeface? {
        // TODO: (nate) support line height
        // TODO: (nate) support inline letter-spacing adjustments
        if (fontName != null) {
            val fontDescriptor = FontDescriptor(name = fontName)
            val typeface = fontManager.get(fontDescriptor)

            if (typeface == null) {
                missingFontsTracker.onFontMissing(fontDescriptor)
            }

            return typeface
        }
        return null
    }

    // the font size of zero will cause a load of possible errors and crash in android, so we clamp it
    val resolvedFontSizeValue: Float
        get() {
            return Math.max(fontSize, 1f)
        }

    // the font unit will depends on wether or not we want to dynamically scale our labels (DIP vs SP)
    fun resolveFontSizeUnit(): Int {
        return if (!isUnscaled) {
            TypedValue.COMPLEX_UNIT_SP
        } else {
            TypedValue.COMPLEX_UNIT_DIP
        }
    }

    private fun resolveFontSize(context: Context): Float {
        val resources = context.resources ?: Resources.getSystem()
        return TypedValue.applyDimension(
            resolveFontSizeUnit(),
            fontSize,
            resources.displayMetrics
        )
    }

    /**
     * Creates the matching Paint object for a given FontAttributes
     * - we choose not to include alignment here, since positioning should be dependent on the layout
     */
    fun toPaint(fontManager: FontManager, missingFontsTracker: MissingFontsTracker): Paint {
        return Paint().apply {
            textSize = resolveFontSize(fontManager.context)
            typeface = resolveTypeface(fontManager, missingFontsTracker)
            color = outlineColor ?: Color.TRANSPARENT
            strokeWidth = outlineWidth ?: 0f
            isUnderlineText = textDecoration == TextDecoration.UNDERLINE
            isStrikeThruText = textDecoration == TextDecoration.STRIKETHROUGH
            style = Paint.Style.STROKE
        }
    }
}
