# Valdi for Flutter Developers: Comprehensive Learning Guide

This guide provides a structured learning path for Flutter developers who want to deeply understand and contribute to the Valdi framework. It covers framework architecture, build systems, platform integration, and practical migration patterns.

## Table of Contents

1. [Quick Start: Flutter to Valdi Mapping](#quick-start-flutter-to-valdi-mapping)
2. [Framework Architecture Deep Dive](#framework-architecture-deep-dive)
3. [Build System & Platform Integration](#build-system--platform-integration)
4. [Learning Path & Exercises](#learning-path--exercises)
5. [Contribution Readiness](#contribution-readiness)
6. [Hands-on Projects](#hands-on-projects)

## Quick Start: Flutter to Valdi Mapping

### Core Concepts Comparison

| Flutter Concept | Valdi Equivalent | Key Differences |
|----------------|------------------|-----------------|
| `Widget` | `Component` | Valdi components are class-based, not functional |
| `StatelessWidget` | `Component<ViewModel>` | Similar concept, props-driven |
| `StatefulWidget` | `StatefulComponent<ViewModel, State>` | Similar state management |
| `build()` method | `onRender()` method | Returns `void`, uses JSX side effects |
| `setState()` | `setState()` | Nearly identical API |
| Dart | TypeScript | Type-safe, but different syntax |
| `pubspec.yaml` | `module.yaml` | Different dependency management |
| Flutter SDK | Valdi Compiler + Runtime | Different compilation approach |

### Language Transition: Dart to TypeScript

**Dart (Flutter):**
```dart
class Counter extends StatefulWidget {
  final String title;
  
  const Counter({Key? key, required this.title}) : super(key: key);
  
  @override
  State<Counter> createState() => _CounterState();
}

class _CounterState extends State<Counter> {
  int _counter = 0;
  
  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Text(widget.title),
          Text('$_counter'),
          ElevatedButton(
            onPressed: _incrementCounter,
            child: Text('Increment'),
          ),
        ],
      ),
    );
  }
}
```

**TypeScript (Valdi):**
```tsx
interface ViewModel {
  title: string;
}

interface State {
  counter: number;
}

export class Counter extends StatefulComponent<ViewModel, State> {
  state = {
    counter: 0,
  };
  
  private incrementCounter = () => {
    this.setState({
      counter: this.state.counter + 1,
    });
  };
  
  onRender(): void {
    <layout direction="column">
      <label value={this.viewModel.title} />
      <label value={this.state.counter.toString()} />
      <view onTap={this.incrementCounter}>
        <label value="Increment" />
      </view>
    </layout>;
  }
}
```

### Key TypeScript Concepts for Flutter Developers

1. **Interfaces vs Classes**: TypeScript uses interfaces for type definitions
2. **Arrow Functions**: `() => {}` syntax for maintaining `this` context
3. **Optional Properties**: `property?: type` syntax
4. **Generic Types**: `Component<ViewModel, State>` for type safety
5. **Module System**: `import/export` instead of Dart's `import`

## Framework Architecture Deep Dive

### 1. Compilation Pipeline Comparison

**Flutter Architecture:**
```
Dart Source → Dart Compiler → Native ARM/x64 Code
                           → JavaScript (Web)
```

**Valdi Architecture:**
```
TypeScript/TSX → Valdi Compiler → .valdimodule files → Native Views
                                → JavaScript Runtime → Platform Integration
```

### 2. Component Lifecycle Mapping

| Flutter Lifecycle | Valdi Lifecycle | Purpose |
|------------------|-----------------|---------|
| `initState()` | `onCreate()` | Component initialization |
| `build()` | `onRender()` | UI rendering |
| `setState()` | `setState()` | State updates |
| `dispose()` | `onDestroy()` | Cleanup |
| N/A | `onViewModelUpdate()` | Props/ViewModel changes |

### 3. Rendering System Differences

**Flutter's Widget Tree:**
- Immutable widgets
- Element tree for state
- RenderObject tree for layout/paint

**Valdi's Component System:**
- Mutable component instances
- Virtual node tree for reconciliation
- Native view tree for rendering

### 4. State Management Patterns

**Flutter (Provider/Bloc):**
```dart
class CounterProvider extends ChangeNotifier {
  int _count = 0;
  int get count => _count;
  
  void increment() {
    _count++;
    notifyListeners();
  }
}

// Usage
Consumer<CounterProvider>(
  builder: (context, counter, child) {
    return Text('${counter.count}');
  },
)
```

**Valdi (Provider/RxJS):**
```tsx
interface CounterStore {
  count: number;
  increment(): void;
}

// Usage with Provider
<Provider value={counterStore}>
  <CounterDisplay />
</Provider>

// In component
export class CounterDisplay extends Component<{}, {}, CounterStore> {
  onRender(): void {
    <label value={this.context.count.toString()} />;
  }
}
```

## Build System & Platform Integration

### 1. Build System Comparison

**Flutter Build Process:**
```bash
flutter build ios     # Creates iOS app bundle
flutter build android # Creates Android APK/AAB
```

**Valdi Build Process:**
```bash
valdi install ios     # Sets up iOS integration
valdi install android # Sets up Android integration
valdi hotreload      # Development hot reload
```

### 2. Platform Integration Patterns

**Flutter Platform Channels:**
```dart
// Dart side
const platform = MethodChannel('com.example/native');
final result = await platform.invokeMethod('getNativeData');

// Native iOS (Swift)
@objc class NativeHandler: NSObject, FlutterPlugin {
  static func register(with registrar: FlutterPluginRegistrar) {
    // Registration code
  }
}
```

**Valdi Native Annotations:**
```tsx
// TypeScript side
interface NativeModule {
  getNativeData(): string;
}

// Native iOS (Objective-C)
@interface MyNativeModule : NSObject<NativeModuleProtocol>
@end

@implementation MyNativeModule
VALDI_REGISTER_MODULE()

- (NSString *)getNativeData {
  return @"Native data";
}
@end
```

### 3. Module System Architecture

**Valdi Module Structure:**
```
my_module/
├── BUILD.bazel          # Build configuration
├── module.yaml          # Module metadata
├── tsconfig.json        # TypeScript config
├── src/
│   ├── Component.tsx    # Main component
│   └── types.ts         # Type definitions
├── res/                 # Resources (images, etc.)
└── test/               # Unit tests
```

**Key Build Files:**

`module.yaml`:
```yaml
output_target: release
dependencies:
  - valdi_core
  - valdi_tsx
compilation_mode:
  js_bytecode: {}
```

`BUILD.bazel`:
```python
valdi_module(
    name = "my_module",
    srcs = glob(["src/**/*.ts", "src/**/*.tsx"]),
    deps = [
        "//src/valdi_modules/src/valdi/valdi_core",
        "//src/valdi_modules/src/valdi/valdi_tsx",
    ],
    ios_module_name = "SCCMyModule",
    android_output_target = "release",
    ios_output_target = "release",
)
```

## Learning Path & Exercises

### Phase 1: Foundation (Week 1-2)

**Prerequisites:**
- Basic TypeScript knowledge
- Understanding of React concepts
- Familiarity with mobile development

**Learning Objectives:**
1. Understand Valdi's component model
2. Learn TypeScript specifics for Valdi
3. Set up development environment

**Exercises:**

1. **Hello World Component**
```tsx
// Exercise: Create a simple greeting component
interface GreetingViewModel {
  name: string;
  age?: number;
}

export class Greeting extends Component<GreetingViewModel> {
  onRender(): void {
    // TODO: Implement greeting display
    // Show name and optionally age
    // Add some basic styling
  }
}
```

2. **Counter with State**
```tsx
// Exercise: Implement a counter with increment/decrement
interface CounterState {
  count: number;
  step: number;
}

export class Counter extends StatefulComponent<{}, CounterState> {
  state = {
    count: 0,
    step: 1,
  };
  
  // TODO: Implement increment, decrement, and reset methods
  // TODO: Add buttons for each action
  // TODO: Display current count with styling
}
```

### Phase 2: Architecture Understanding (Week 3-4)

**Learning Objectives:**
1. Understand compilation pipeline
2. Learn about native integration
3. Explore performance characteristics

**Study Materials:**
- [Architecture Documentation](./architecture.md)
- Valdi compiler source code
- Runtime implementation

**Exercises:**

3. **Module Creation**
```bash
# Exercise: Create a complete Valdi module
mkdir my_feature_module
cd my_feature_module

# TODO: Set up proper module structure
# TODO: Create BUILD.bazel file
# TODO: Create module.yaml
# TODO: Implement a feature with multiple components
```

4. **Native Integration**
```tsx
// Exercise: Create a component that uses native functionality
interface NativeServiceModule {
  getDeviceInfo(): DeviceInfo;
  showNativeAlert(message: string): void;
}

export class DeviceInfoDisplay extends Component<{}, {}, NativeServiceModule> {
  // TODO: Display device information from native module
  // TODO: Add button to show native alert
}
```

### Phase 3: Advanced Concepts (Week 5-6)

**Learning Objectives:**
1. Master performance optimization
2. Understand advanced patterns
3. Learn testing strategies

**Exercises:**

5. **Performance Optimization**
```tsx
// Exercise: Optimize a list component for performance
interface ListItem {
  id: string;
  title: string;
  description: string;
}

interface ListViewModel {
  items: ListItem[];
}

export class OptimizedList extends Component<ListViewModel> {
  // TODO: Implement efficient list rendering
  // TODO: Add view recycling
  // TODO: Implement lazy loading
}
```

6. **Animation System**
```tsx
// Exercise: Create animated transitions
export class AnimatedCard extends StatefulComponent<CardViewModel, CardState> {
  private animateExpand = () => {
    this.setStateAnimated(
      { expanded: !this.state.expanded },
      { duration: 300, curve: 'easeInOut' }
    );
  };
  
  // TODO: Implement expandable card with smooth animations
  // TODO: Add gesture handling
}
```

## Contribution Readiness

### Development Environment Setup

1. **Required Tools:**
```bash
# Install Valdi CLI
cd Valdi/npm_modules/cli/
npm run cli:install
valdi dev_setup

# Install VSCode extensions
scripts/vscode/install_extensions.sh
```

2. **Codebase Navigation:**

**Key Directories to Study:**
- `src/valdi_modules/src/valdi/valdi_core/` - Core framework
- `composer/src/composer/runtime/` - C++ runtime
- `compiler/compiler/Compiler/` - Swift compiler
- `valdi_core/src/valdi_core/` - Platform integration
- `apps/` - Example applications

**Important Files for Contributors:**
- `src/valdi_modules/src/valdi/valdi_core/src/Component.ts` - Component system
- `src/valdi_modules/src/valdi/valdi_core/src/Renderer.ts` - Rendering engine
- `composer/src/composer/runtime/JavaScript/JavaScriptRuntime.cpp` - JS runtime
- `valdi_core/src/valdi_core/cpp/JavaScriptBridge.cpp` - Native bridge

### Testing Strategy

**Unit Testing:**
```tsx
// Example test structure
import { TestRenderer } from 'valdi_test';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    const renderer = new TestRenderer();
    const component = renderer.render(MyComponent, { title: 'Test' });
    
    expect(component.findByText('Test')).toBeTruthy();
  });
});
```

**Integration Testing:**
```bash
# Run platform-specific tests
bazel test //apps/helloworld:test_ios
bazel test //apps/helloworld:test_android
```

### Contribution Guidelines

1. **Code Style:**
   - Follow TypeScript best practices
   - Use meaningful variable names
   - Add comprehensive documentation
   - Write unit tests for new features

2. **Performance Considerations:**
   - Minimize JavaScript-to-native bridge calls
   - Use component pooling for lists
   - Implement proper cleanup in `onDestroy()`
   - Profile memory usage

3. **Platform Compatibility:**
   - Test on both iOS and Android
   - Handle platform-specific differences
   - Use native annotations for platform APIs

## Hands-on Projects

### Project 1: Todo App (Beginner)
Build a complete todo application demonstrating:
- Component composition
- State management
- Local storage integration
- Basic animations

### Project 2: Chat Interface (Intermediate)
Create a chat interface showcasing:
- List performance optimization
- Real-time updates
- Native integration (camera, file picker)
- Custom animations

### Project 3: Framework Extension (Advanced)
Contribute to Valdi by:
- Adding a new core component
- Implementing a performance optimization
- Creating developer tools
- Writing comprehensive documentation

## Next Steps

1. **Start with Phase 1 exercises** - Build foundation knowledge
2. **Study the architecture document** - Understand internal workings
3. **Explore example applications** - Learn from existing code
4. **Join the community** - Connect with other developers
5. **Contribute incrementally** - Start with small improvements

## Resources

- [Valdi Architecture](./architecture.md) - Deep technical overview
- [Component System](./core-component.md) - Component lifecycle and patterns
- [Native Integration](./native-bindings.md) - Platform-specific development
- [Performance Guide](./performance-optimization.md) - Optimization strategies
- [Discord Community](https://discord.gg/sqMERrCVYF) - Developer support

This guide provides a comprehensive foundation for Flutter developers transitioning to Valdi. Focus on hands-on practice and gradually build up to contributing to the framework itself.

---

## Deep Dive: Build System Architecture

### Understanding Bazel vs Flutter's Build System

**Flutter's Build Process:**
- Uses Gradle (Android) and Xcode (iOS)
- Dart compilation to native code
- Asset bundling through Flutter tools
- Platform-specific build configurations

**Valdi's Bazel-Based System:**
- Unified build system across platforms
- Incremental compilation and caching
- Dependency management through BUILD.bazel files
- Cross-platform module compilation

### Compilation Modes Explained

Valdi supports three compilation modes, each optimized for different scenarios:

1. **JavaScript Mode (`js`)**
   - Fastest compilation for development
   - JavaScript source maps for debugging
   - Hot reload support
   - Best for rapid iteration

2. **JavaScript Bytecode Mode (`js_bytecode`)**
   - Optimized JavaScript with bytecode compilation
   - Better performance than pure JS
   - Smaller bundle sizes
   - Good balance for most applications

3. **Native Mode (`native`)**
   - Full native code generation
   - Maximum runtime performance
   - Longer compilation times
   - Best for production releases

**Configuration Example:**
```yaml
# module.yaml
compilation_mode:
  js_bytecode:
    include_patterns:
      - .*/src/performance-critical/.*
  native:
    include_patterns:
      - .*/src/core/.*
```

### Platform-Specific Integration Patterns

**iOS Integration:**
```objc
// SCMyModuleFactory.m
#import "valdi_core/SCValdiModuleFactoryRegistry.h"
#import <SCCMyModuleTypes/SCCMyModuleTypes.h>

@interface SCMyModule: NSObject<SCCMyModuleProtocol>
@end

@implementation SCMyModule

- (NSString *)platformName {
    return @"iOS";
}

- (void)performNativeAction:(NSString *)action {
    // iOS-specific implementation
    dispatch_async(dispatch_get_main_queue(), ^{
        // UI updates on main thread
    });
}

@end

@interface SCMyModuleFactory : SCCMyModuleFactory
@end

@implementation SCMyModuleFactory

VALDI_REGISTER_MODULE()

- (id<SCCMyModuleProtocol>)onLoadModule {
    return [SCMyModule new];
}

@end
```

**Android Integration:**
```kotlin
// MyModuleFactory.kt
import com.snap.valdi.modules.mymodule.MyModuleModule
import com.snap.valdi.modules.mymodule.MyModuleModuleFactory

class MyModule : MyModuleModule {
    override fun getPlatformName(): String = "Android"

    override fun performNativeAction(action: String) {
        // Android-specific implementation
        runOnUiThread {
            // UI updates on main thread
        }
    }
}

class MyModuleFactory : MyModuleModuleFactory() {
    override fun onLoadModule(): MyModuleModule {
        return MyModule()
    }
}
```

### Advanced Component Patterns

**Higher-Order Components (HOCs):**
```tsx
// Similar to Flutter's wrapper widgets
function withLoading<T extends Component<any>>(
  WrappedComponent: ComponentConstructor<T>
): ComponentConstructor<T> {
  return class extends WrappedComponent {
    onRender(): void {
      if (this.isLoading()) {
        <view>
          <label value="Loading..." />
        </view>;
      } else {
        super.onRender();
      }
    }

    private isLoading(): boolean {
      // Loading logic
      return false;
    }
  };
}

// Usage
const LoadingAwareComponent = withLoading(MyComponent);
```

**Provider Pattern (Context API):**
```tsx
// Theme provider similar to Flutter's Theme widget
interface ThemeData {
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
}

const defaultTheme: ThemeData = {
  primaryColor: '#007AFF',
  backgroundColor: '#FFFFFF',
  textColor: '#000000',
};

export class ThemeProvider extends Component<{ theme?: ThemeData; children?: () => void }> {
  onRender(): void {
    const theme = this.viewModel.theme || defaultTheme;

    <Provider value={theme}>
      {this.viewModel.children?.()}
    </Provider>;
  }
}

// Usage in components
export class ThemedButton extends Component<ButtonViewModel, {}, ThemeData> {
  onRender(): void {
    const theme = this.context;

    <view style={{ backgroundColor: theme.primaryColor }}>
      <label value={this.viewModel.title} style={{ color: theme.textColor }} />
    </view>;
  }
}
```

### Performance Optimization Strategies

**1. Component Memoization:**
```tsx
// Similar to Flutter's const constructors
export class MemoizedComponent extends Component<ComplexViewModel> {
  private lastViewModel?: ComplexViewModel;
  private cachedResult?: any;

  onRender(): void {
    if (this.shouldRecompute()) {
      this.cachedResult = this.computeExpensiveValue();
      this.lastViewModel = this.viewModel;
    }

    this.renderWithCachedResult();
  }

  private shouldRecompute(): boolean {
    return !this.lastViewModel ||
           this.lastViewModel.expensiveProperty !== this.viewModel.expensiveProperty;
  }
}
```

**2. List Optimization:**
```tsx
// Similar to Flutter's ListView.builder
interface ListItemData {
  id: string;
  content: string;
}

export class VirtualizedList extends Component<{ items: ListItemData[] }> {
  private visibleRange = { start: 0, end: 10 };

  onRender(): void {
    const visibleItems = this.viewModel.items.slice(
      this.visibleRange.start,
      this.visibleRange.end
    );

    <scroll onScroll={this.handleScroll}>
      {visibleItems.map(item => (
        <ListItem key={item.id} data={item} />
      ))}
    </scroll>;
  }

  private handleScroll = (event: ScrollEvent) => {
    // Update visible range based on scroll position
    this.updateVisibleRange(event.contentOffset.y);
  };
}
```

### Memory Management Best Practices

**1. Subscription Management:**
```tsx
export class DataSubscriberComponent extends StatefulComponent<{}, { data: any[] }> {
  private subscription?: Subscription;

  onCreate(): void {
    this.subscription = dataService.subscribe(data => {
      this.setState({ data });
    });
  }

  onDestroy(): void {
    // Critical: Always clean up subscriptions
    this.subscription?.unsubscribe();
  }
}
```

**2. Event Listener Cleanup:**
```tsx
export class EventListenerComponent extends Component<{}> {
  private handleGlobalEvent = (event: CustomEvent) => {
    // Handle event
  };

  onCreate(): void {
    globalEventEmitter.addEventListener('customEvent', this.handleGlobalEvent);
  }

  onDestroy(): void {
    globalEventEmitter.removeEventListener('customEvent', this.handleGlobalEvent);
  }
}
```

### Testing Strategies for Valdi Components

**Unit Testing with Mocks:**
```tsx
import { TestRenderer } from 'valdi_test';
import { MockNativeModule } from './mocks/MockNativeModule';

describe('NativeIntegrationComponent', () => {
  let mockNativeModule: MockNativeModule;
  let renderer: TestRenderer;

  beforeEach(() => {
    mockNativeModule = new MockNativeModule();
    renderer = new TestRenderer();
  });

  it('should call native method on button tap', () => {
    const component = renderer.render(
      NativeIntegrationComponent,
      {},
      mockNativeModule
    );

    const button = component.findByAccessibilityId('native-action-button');
    button.tap();

    expect(mockNativeModule.performAction).toHaveBeenCalledWith('test-action');
  });
});
```

**Integration Testing:**
```tsx
// Test complete user flows
describe('TodoApp Integration', () => {
  it('should add and complete todos', async () => {
    const app = await TestApp.launch(TodoApp);

    // Add a todo
    await app.typeText('input-field', 'Buy groceries');
    await app.tap('add-button');

    // Verify todo appears
    expect(app.findByText('Buy groceries')).toBeTruthy();

    // Complete the todo
    await app.tap('todo-checkbox');

    // Verify completion
    expect(app.findByAccessibilityId('completed-todo')).toBeTruthy();
  });
});
```

### Debugging and Development Tools

**1. Valdi Inspector Usage:**
```bash
# Start inspector for debugging
valdi inspector

# Connect to running app
# Navigate to http://localhost:8080
# Inspect component tree, state, and performance
```

**2. Performance Profiling:**
```tsx
export class ProfiledComponent extends Component<{}> {
  onRender(): void {
    const startTime = performance.now();

    // Component rendering logic
    this.renderContent();

    const endTime = performance.now();
    console.log(`Render time: ${endTime - startTime}ms`);
  }
}
```

**3. Hot Reload Best Practices:**
```tsx
// Structure code for optimal hot reload
export class HotReloadFriendlyComponent extends StatefulComponent<{}, State> {
  // Keep state initialization simple
  state = this.getInitialState();

  private getInitialState(): State {
    // Complex initialization logic in separate method
    return {
      // Initial state
    };
  }

  // Use arrow functions for event handlers to maintain 'this' binding
  private handleAction = () => {
    // Handler logic
  };
}
```

---

## Practical Exercises: Complete Working Examples

### Exercise 1: Flutter Weather App → Valdi Weather App

**Flutter Version (Reference):**
```dart
class WeatherApp extends StatefulWidget {
  @override
  _WeatherAppState createState() => _WeatherAppState();
}

class _WeatherAppState extends State<WeatherApp> {
  String? weather;
  bool isLoading = false;

  Future<void> fetchWeather(String city) async {
    setState(() => isLoading = true);

    try {
      final response = await http.get(
        Uri.parse('https://api.weather.com/v1/current?city=$city')
      );
      setState(() {
        weather = json.decode(response.body)['temperature'];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        weather = 'Error loading weather';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Weather App')),
      body: Column(
        children: [
          TextField(
            onSubmitted: fetchWeather,
            decoration: InputDecoration(hintText: 'Enter city name'),
          ),
          if (isLoading) CircularProgressIndicator(),
          if (weather != null) Text('Temperature: $weather°C'),
        ],
      ),
    );
  }
}
```

**Valdi Version (Implementation):**

First, create the module structure:
```bash
mkdir weather_app_module
cd weather_app_module
```

`module.yaml`:
```yaml
output_target: release
dependencies:
  - valdi_core
  - valdi_tsx
  - valdi_http
compilation_mode:
  js_bytecode: {}
```

`BUILD.bazel`:
```python
load("//bzl/valdi:valdi_module.bzl", "valdi_module")

valdi_module(
    name = "weather_app",
    srcs = glob([
        "src/**/*.ts",
        "src/**/*.tsx",
    ]) + ["tsconfig.json"],
    deps = [
        "//src/valdi_modules/src/valdi/valdi_core",
        "//src/valdi_modules/src/valdi/valdi_tsx",
        "//src/valdi_modules/src/valdi/valdi_http",
    ],
    ios_module_name = "SCCWeatherApp",
    android_output_target = "release",
    ios_output_target = "release",
)
```

`src/WeatherApp.tsx`:
```tsx
import { StatefulComponent } from 'valdi_core/src/Component';
import { HttpClient } from 'valdi_http/src/HttpClient';

interface WeatherState {
  weather?: string;
  isLoading: boolean;
  error?: string;
}

interface WeatherResponse {
  temperature: number;
  description: string;
}

export class WeatherApp extends StatefulComponent<{}, WeatherState> {
  state: WeatherState = {
    isLoading: false,
  };

  private httpClient = new HttpClient();

  private fetchWeather = async (city: string) => {
    if (!city.trim()) return;

    this.setState({ isLoading: true, error: undefined });

    try {
      const response = await this.httpClient.get<WeatherResponse>(
        `https://api.weather.com/v1/current?city=${encodeURIComponent(city)}`
      );

      this.setState({
        weather: `${response.temperature}°C - ${response.description}`,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        error: 'Failed to load weather data',
        isLoading: false,
      });
    }
  };

  private handleCitySubmit = (city: string) => {
    this.fetchWeather(city);
  };

  onRender(): void {
    <layout direction="column" style={{ padding: 16 }}>
      <label
        value="Weather App"
        style={{
          fontSize: 24,
          fontWeight: 'bold',
          marginBottom: 20
        }}
      />

      <CityInput onSubmit={this.handleCitySubmit} />

      {this.state.isLoading && (
        <view style={{ marginTop: 20 }}>
          <LoadingSpinner />
        </view>
      )}

      {this.state.weather && (
        <WeatherDisplay weather={this.state.weather} />
      )}

      {this.state.error && (
        <ErrorDisplay message={this.state.error} />
      )}
    </layout>;
  }
}
```

`src/CityInput.tsx`:
```tsx
import { StatefulComponent } from 'valdi_core/src/Component';

interface CityInputViewModel {
  onSubmit: (city: string) => void;
}

interface CityInputState {
  inputValue: string;
}

export class CityInput extends StatefulComponent<CityInputViewModel, CityInputState> {
  state: CityInputState = {
    inputValue: '',
  };

  private handleTextChange = (text: string) => {
    this.setState({ inputValue: text });
  };

  private handleSubmit = () => {
    if (this.state.inputValue.trim()) {
      this.viewModel.onSubmit(this.state.inputValue.trim());
      this.setState({ inputValue: '' });
    }
  };

  onRender(): void {
    <layout direction="row" style={{ marginBottom: 16 }}>
      <text-input
        value={this.state.inputValue}
        placeholder="Enter city name"
        onTextChange={this.handleTextChange}
        onSubmit={this.handleSubmit}
        style={{
          flex: 1,
          padding: 12,
          borderWidth: 1,
          borderColor: '#ccc',
          borderRadius: 8,
          marginRight: 8,
        }}
      />
      <view
        onTap={this.handleSubmit}
        style={{
          backgroundColor: '#007AFF',
          padding: 12,
          borderRadius: 8,
          justifyContent: 'center',
        }}
      >
        <label
          value="Search"
          style={{ color: 'white', fontWeight: 'bold' }}
        />
      </view>
    </layout>;
  }
}
```

### Exercise 2: State Management with RxJS (Advanced)

**Creating a Global State Store:**

`src/store/WeatherStore.ts`:
```tsx
import { BehaviorSubject, Observable } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';

interface WeatherState {
  currentWeather?: WeatherData;
  favorites: string[];
  isLoading: boolean;
  error?: string;
}

interface WeatherData {
  city: string;
  temperature: number;
  description: string;
  timestamp: number;
}

export class WeatherStore {
  private state$ = new BehaviorSubject<WeatherState>({
    favorites: [],
    isLoading: false,
  });

  // Selectors
  getCurrentWeather(): Observable<WeatherData | undefined> {
    return this.state$.pipe(
      map(state => state.currentWeather),
      distinctUntilChanged()
    );
  }

  getFavorites(): Observable<string[]> {
    return this.state$.pipe(
      map(state => state.favorites),
      distinctUntilChanged()
    );
  }

  getIsLoading(): Observable<boolean> {
    return this.state$.pipe(
      map(state => state.isLoading),
      distinctUntilChanged()
    );
  }

  // Actions
  setLoading(isLoading: boolean): void {
    this.updateState({ isLoading });
  }

  setWeather(weather: WeatherData): void {
    this.updateState({
      currentWeather: weather,
      isLoading: false,
      error: undefined
    });
  }

  setError(error: string): void {
    this.updateState({
      error,
      isLoading: false
    });
  }

  addFavorite(city: string): void {
    const currentState = this.state$.value;
    if (!currentState.favorites.includes(city)) {
      this.updateState({
        favorites: [...currentState.favorites, city]
      });
    }
  }

  removeFavorite(city: string): void {
    const currentState = this.state$.value;
    this.updateState({
      favorites: currentState.favorites.filter(fav => fav !== city)
    });
  }

  private updateState(partial: Partial<WeatherState>): void {
    const currentState = this.state$.value;
    this.state$.next({ ...currentState, ...partial });
  }
}

// Global store instance
export const weatherStore = new WeatherStore();
```

**Connected Component with RxJS:**

`src/ConnectedWeatherApp.tsx`:
```tsx
import { StatefulComponent } from 'valdi_core/src/Component';
import { Subscription } from 'rxjs';
import { weatherStore, WeatherData } from './store/WeatherStore';

interface ConnectedWeatherState {
  currentWeather?: WeatherData;
  favorites: string[];
  isLoading: boolean;
}

export class ConnectedWeatherApp extends StatefulComponent<{}, ConnectedWeatherState> {
  state: ConnectedWeatherState = {
    favorites: [],
    isLoading: false,
  };

  private subscriptions: Subscription[] = [];

  onCreate(): void {
    // Subscribe to store updates
    this.subscriptions.push(
      weatherStore.getCurrentWeather().subscribe(weather => {
        this.setState({ currentWeather: weather });
      }),

      weatherStore.getFavorites().subscribe(favorites => {
        this.setState({ favorites });
      }),

      weatherStore.getIsLoading().subscribe(isLoading => {
        this.setState({ isLoading });
      })
    );
  }

  onDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private handleAddFavorite = (city: string) => {
    weatherStore.addFavorite(city);
  };

  private handleRemoveFavorite = (city: string) => {
    weatherStore.removeFavorite(city);
  };

  onRender(): void {
    <layout direction="column" style={{ padding: 16 }}>
      <WeatherHeader />

      <CityInput onSubmit={this.fetchWeatherForCity} />

      {this.state.isLoading && <LoadingSpinner />}

      {this.state.currentWeather && (
        <WeatherCard
          weather={this.state.currentWeather}
          onAddFavorite={this.handleAddFavorite}
          isFavorite={this.state.favorites.includes(this.state.currentWeather.city)}
        />
      )}

      <FavoritesList
        favorites={this.state.favorites}
        onRemoveFavorite={this.handleRemoveFavorite}
        onSelectFavorite={this.fetchWeatherForCity}
      />
    </layout>;
  }

  private fetchWeatherForCity = async (city: string) => {
    weatherStore.setLoading(true);

    try {
      const weatherData = await this.weatherService.fetchWeather(city);
      weatherStore.setWeather(weatherData);
    } catch (error) {
      weatherStore.setError('Failed to fetch weather data');
    }
  };
}
```

### Exercise 3: Native Module Integration

**Creating a Location Service Module:**

`src/native/LocationService.ts`:
```tsx
// TypeScript interface definition
export interface LocationService {
  getCurrentLocation(): Promise<LocationData>;
  requestLocationPermission(): Promise<boolean>;
  startLocationUpdates(callback: (location: LocationData) => void): void;
  stopLocationUpdates(): void;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}
```

**iOS Implementation:**

`src/ios/LocationServiceFactory.m`:
```objc
#import "valdi_core/SCValdiModuleFactoryRegistry.h"
#import <SCCWeatherAppTypes/SCCWeatherAppTypes.h>
#import <CoreLocation/CoreLocation.h>

@interface SCLocationService : NSObject<SCCWeatherAppLocationServiceModule, CLLocationManagerDelegate>
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) void (^locationCallback)(SCCWeatherAppLocationData *);
@end

@implementation SCLocationService

- (instancetype)init {
    self = [super init];
    if (self) {
        self.locationManager = [[CLLocationManager alloc] init];
        self.locationManager.delegate = self;
    }
    return self;
}

- (void)getCurrentLocation:(SCComposerMarshaller *)marshaller {
    [self.locationManager requestLocation];
    // Implementation details...
}

- (void)requestLocationPermission:(SCComposerMarshaller *)marshaller {
    [self.locationManager requestWhenInUseAuthorization];
    // Implementation details...
}

- (void)startLocationUpdates:(SCComposerMarshaller *)marshaller {
    [self.locationManager startUpdatingLocation];
    // Implementation details...
}

- (void)stopLocationUpdates:(SCComposerMarshaller *)marshaller {
    [self.locationManager stopUpdatingLocation];
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager
     didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;

    SCCWeatherAppLocationData *locationData = [[SCCWeatherAppLocationData alloc] init];
    locationData.latitude = location.coordinate.latitude;
    locationData.longitude = location.coordinate.longitude;
    locationData.accuracy = location.horizontalAccuracy;
    locationData.timestamp = (int64_t)(location.timestamp.timeIntervalSince1970 * 1000);

    if (self.locationCallback) {
        self.locationCallback(locationData);
    }
}

@end

@interface SCLocationServiceFactory : SCCWeatherAppLocationServiceModuleFactory
@end

@implementation SCLocationServiceFactory

VALDI_REGISTER_MODULE()

- (id<SCCWeatherAppLocationServiceModule>)onLoadModule {
    return [[SCLocationService alloc] init];
}

@end
```

**Using the Native Module in Components:**

`src/LocationWeatherApp.tsx`:
```tsx
import { StatefulComponent } from 'valdi_core/src/Component';
import { LocationService, LocationData } from './native/LocationService';

interface LocationWeatherState {
  location?: LocationData;
  weather?: WeatherData;
  permissionGranted: boolean;
  isLoadingLocation: boolean;
}

export class LocationWeatherApp extends StatefulComponent<
  {},
  LocationWeatherState,
  LocationService
> {
  state: LocationWeatherState = {
    permissionGranted: false,
    isLoadingLocation: false,
  };

  onCreate(): void {
    this.requestLocationPermission();
  }

  onDestroy(): void {
    this.context.stopLocationUpdates();
  }

  private requestLocationPermission = async () => {
    try {
      const granted = await this.context.requestLocationPermission();
      this.setState({ permissionGranted: granted });

      if (granted) {
        this.getCurrentLocation();
      }
    } catch (error) {
      console.error('Failed to request location permission:', error);
    }
  };

  private getCurrentLocation = async () => {
    this.setState({ isLoadingLocation: true });

    try {
      const location = await this.context.getCurrentLocation();
      this.setState({
        location,
        isLoadingLocation: false
      });

      // Fetch weather for current location
      this.fetchWeatherForLocation(location);
    } catch (error) {
      this.setState({ isLoadingLocation: false });
      console.error('Failed to get current location:', error);
    }
  };

  private fetchWeatherForLocation = async (location: LocationData) => {
    // Implementation to fetch weather based on coordinates
    try {
      const weather = await this.weatherService.fetchWeatherByCoordinates(
        location.latitude,
        location.longitude
      );
      this.setState({ weather });
    } catch (error) {
      console.error('Failed to fetch weather for location:', error);
    }
  };

  onRender(): void {
    <layout direction="column" style={{ padding: 16 }}>
      <label
        value="Location-Based Weather"
        style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}
      />

      {!this.state.permissionGranted && (
        <view style={{ marginBottom: 20 }}>
          <label value="Location permission required for weather updates" />
          <view onTap={this.requestLocationPermission} style={{ marginTop: 10 }}>
            <label value="Grant Permission" style={{ color: '#007AFF' }} />
          </view>
        </view>
      )}

      {this.state.isLoadingLocation && (
        <view style={{ marginBottom: 20 }}>
          <LoadingSpinner />
          <label value="Getting your location..." />
        </view>
      )}

      {this.state.location && (
        <LocationDisplay location={this.state.location} />
      )}

      {this.state.weather && (
        <WeatherCard weather={this.state.weather} />
      )}

      <view onTap={this.getCurrentLocation} style={{ marginTop: 20 }}>
        <label value="Refresh Location" style={{ color: '#007AFF' }} />
      </view>
    </layout>;
  }
}
```

These exercises demonstrate:
1. **Complete module setup** with proper build configuration
2. **State management patterns** using both local state and global stores
3. **Native integration** with platform-specific implementations
4. **Real-world patterns** like async operations, error handling, and cleanup
5. **Performance considerations** with proper subscription management

Each exercise builds upon the previous one, gradually introducing more complex concepts while maintaining practical, working examples that you can run and modify.
