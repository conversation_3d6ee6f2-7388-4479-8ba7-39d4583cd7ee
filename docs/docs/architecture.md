# Valdi Architecture

## Table of Contents

1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Core Components](#core-components)
4. [Compilation Pipeline](#compilation-pipeline)
5. [Runtime Architecture](#runtime-architecture)
6. [Component Lifecycle](#component-lifecycle)
7. [Rendering Pipeline](#rendering-pipeline)
8. [Data Flow Patterns](#data-flow-patterns)
9. [Performance Architecture](#performance-architecture)
10. [Integration Points](#integration-points)
11. [Extensibility](#extensibility)

## Introduction

Valdi is a cross-platform UI framework that compiles TypeScript code into native mobile applications for iOS and Android. This document provides a comprehensive overview of Valdi's architecture, designed for developers who want to understand the framework's internals, contribute to the codebase, or make informed architectural decisions when building applications with Valdi.

> **Note**: This architecture documentation is based on analysis of key parts of the Valdi codebase. While core components, APIs, and patterns have been verified against the source code, this document represents a high-level architectural overview and may not capture every implementation detail or edge case. Readers should refer to the actual source code for definitive implementation details.

### Design Philosophy

Valdi is built on several key architectural principles:

- **Performance First**: Equal to or better than native performance
- **Cross-Platform Consistency**: Identical behavior across iOS and Android
- **Developer Experience**: Significantly faster development than traditional native code
- **Type Safety**: Leveraging TypeScript for compile-time safety
- **Declarative UI**: React-like component model with reactive updates
- **Native Integration**: Seamless bridging with platform-specific code

## System Overview

Valdi consists of three main subsystems that work together to transform TypeScript source code into native mobile applications:

```mermaid
graph TB
    subgraph "Development Environment"
        TS[TypeScript Source]
        TSX[TSX Components]
        YAML[Module Configuration]
    end
    
    subgraph "Compilation System"
        COMPILER[Valdi Compiler]
        PIPELINE[Compilation Pipeline]
        CODEGEN[Code Generation]
    end
    
    subgraph "Runtime System"
        RUNTIME[Valdi Runtime]
        COMPOSER[Composer Engine]
        JSENGINE[JavaScript Engine]
    end
    
    subgraph "Native Platforms"
        IOS[iOS Application]
        ANDROID[Android Application]
    end
    
    TS --> COMPILER
    TSX --> COMPILER
    YAML --> COMPILER
    
    COMPILER --> PIPELINE
    PIPELINE --> CODEGEN
    CODEGEN --> RUNTIME
    
    RUNTIME --> COMPOSER
    COMPOSER --> JSENGINE
    
    RUNTIME --> IOS
    RUNTIME --> ANDROID
    
    classDef compilation fill:#e1f5fe
    classDef runtime fill:#f3e5f5
    classDef platform fill:#e8f5e8
    
    class COMPILER,PIPELINE,CODEGEN compilation
    class RUNTIME,COMPOSER,JSENGINE runtime
    class IOS,ANDROID platform
```

### System Context

```mermaid
graph LR
    subgraph "External Systems"
        XCODE[Xcode/iOS SDK]
        ANDROID_SDK[Android SDK]
        BAZEL[Bazel Build System]
        NPM[NPM Ecosystem]
    end
    
    subgraph "Valdi Framework"
        VALDI[Valdi Core]
    end
    
    subgraph "Developer Tools"
        IDE[IDE/Editor]
        DEBUGGER[Debugger]
        INSPECTOR[Valdi Inspector]
    end
    
    subgraph "Applications"
        APP[Mobile Applications]
    end
    
    XCODE <--> VALDI
    ANDROID_SDK <--> VALDI
    BAZEL <--> VALDI
    NPM <--> VALDI
    
    IDE <--> VALDI
    DEBUGGER <--> VALDI
    INSPECTOR <--> VALDI
    
    VALDI --> APP
```

## Core Components

### 1. Valdi Compiler

The Valdi Compiler is a Swift-based tool that transforms TypeScript source code into optimized native code and JavaScript bundles.

**Key Responsibilities:**
- TypeScript compilation and type checking
- TSX template processing and optimization
- Native code generation for iOS and Android
- Asset bundling and optimization
- Module dependency resolution

**Architecture:**

```mermaid
graph TD
    subgraph "Compiler Core"
        PARSER[TypeScript Parser]
        ANALYZER[Semantic Analyzer]
        OPTIMIZER[Code Optimizer]
        CODEGEN[Code Generator]
    end
    
    subgraph "Processors"
        TS_PROC[TypeScript Processor]
        TSX_PROC[TSX Processor]
        ASSET_PROC[Asset Processor]
        NATIVE_PROC[Native Code Processor]
    end
    
    subgraph "Outputs"
        VALDIMODULE[.valdimodule Files]
        NATIVE_IOS[iOS Native Code]
        NATIVE_ANDROID[Android Native Code]
        ASSETS[Asset Bundles]
    end
    
    PARSER --> ANALYZER
    ANALYZER --> OPTIMIZER
    OPTIMIZER --> CODEGEN
    
    CODEGEN --> TS_PROC
    CODEGEN --> TSX_PROC
    CODEGEN --> ASSET_PROC
    CODEGEN --> NATIVE_PROC
    
    TS_PROC --> VALDIMODULE
    TSX_PROC --> VALDIMODULE
    ASSET_PROC --> ASSETS
    NATIVE_PROC --> NATIVE_IOS
    NATIVE_PROC --> NATIVE_ANDROID
```

### 2. Valdi Runtime

The Valdi Runtime is the core execution environment that manages component lifecycle, rendering, and native platform integration.

**Key Responsibilities:**
- Component instantiation and lifecycle management
- View tree management and optimization
- Event handling and propagation
- Animation system coordination
- Memory management and garbage collection

### 3. Composer Engine

The Composer Engine is the rendering and layout engine that bridges between the JavaScript runtime and native views.

**Key Responsibilities:**
- View tree reconciliation
- Layout calculation using Yoga (Flexbox)
- Native view creation and management
- Animation execution
- Performance optimization through view recycling

```mermaid
graph TD
    subgraph "Composer Engine"
        RECONCILER[View Reconciler]
        LAYOUT[Layout Engine]
        RENDERER[Native Renderer]
        ANIMATOR[Animation Engine]
    end
    
    subgraph "JavaScript Runtime"
        COMPONENTS[Component Tree]
        STATE[State Management]
        EVENTS[Event Handlers]
    end
    
    subgraph "Native Platform"
        VIEWS[Native Views]
        GESTURES[Gesture Recognizers]
        ANIMATIONS[Core Animation]
    end
    
    COMPONENTS --> RECONCILER
    STATE --> RECONCILER
    RECONCILER --> LAYOUT
    LAYOUT --> RENDERER
    RENDERER --> VIEWS
    
    EVENTS --> GESTURES
    ANIMATOR --> ANIMATIONS
    
    VIEWS --> GESTURES
    GESTURES --> EVENTS
```

## Compilation Pipeline

The Valdi compilation process transforms TypeScript source code through multiple stages to produce optimized native applications.

### Compilation Stages

```mermaid
graph LR
    subgraph "Input"
        SOURCE[TypeScript/TSX Source]
        CONFIG[Module Configuration]
        ASSETS[Static Assets]
    end

    subgraph "Parsing & Analysis"
        PARSE[Parse TypeScript]
        TYPECHECK[Type Checking]
        ANALYZE[Semantic Analysis]
    end

    subgraph "Transformation"
        TSX_TRANSFORM[TSX Transformation]
        OPTIMIZE[Code Optimization]
        BUNDLE[Module Bundling]
    end

    subgraph "Code Generation"
        JS_GEN[JavaScript Generation]
        NATIVE_GEN[Native Code Generation]
        ASSET_GEN[Asset Processing]
    end

    subgraph "Output"
        VALDIMODULE[.valdimodule Bundle]
        NATIVE_CODE[Native Bindings]
        RESOURCES[Resource Bundles]
    end

    SOURCE --> PARSE
    CONFIG --> PARSE
    ASSETS --> PARSE

    PARSE --> TYPECHECK
    TYPECHECK --> ANALYZE

    ANALYZE --> TSX_TRANSFORM
    TSX_TRANSFORM --> OPTIMIZE
    OPTIMIZE --> BUNDLE

    BUNDLE --> JS_GEN
    BUNDLE --> NATIVE_GEN
    BUNDLE --> ASSET_GEN

    JS_GEN --> VALDIMODULE
    NATIVE_GEN --> NATIVE_CODE
    ASSET_GEN --> RESOURCES
```

### Compilation Modes

Valdi supports multiple compilation modes optimized for different scenarios:

1. **JavaScript Mode**: Fast compilation for development and hot reloading
2. **JavaScript Bytecode Mode**: Optimized JavaScript with bytecode compilation
3. **Native Mode**: Full native code generation for maximum performance

### Module System

```mermaid
graph TD
    subgraph "Module Structure"
        MODULE[Valdi Module]
        COMPONENTS[Components]
        SERVICES[Services]
        ASSETS[Assets]
        CONFIG[Configuration]
    end

    subgraph "Dependencies"
        CORE[valdi_core]
        TSX[valdi_tsx]
        COREUTILS[coreutils]
        JASMINE[jasmine]
        SOURCE_MAP[source_map]
        EXTERNAL[External Modules]
    end

    MODULE --> COMPONENTS
    MODULE --> SERVICES
    MODULE --> ASSETS
    MODULE --> CONFIG

    MODULE --> CORE
    MODULE --> TSX
    MODULE --> COREUTILS
    MODULE --> EXTERNAL

    CORE --> TSX
    CORE --> COREUTILS
    CORE --> JASMINE
    CORE --> SOURCE_MAP
```

## Runtime Architecture

### JavaScript Runtime Integration

Valdi integrates with multiple JavaScript engines depending on the platform and configuration:

- **iOS**: JavaScriptCore (default), QuickJS, Hermes, or V8
- **Android**: QuickJS (default), JavaScriptCore (development builds only), Hermes, or V8
- **macOS/Linux**: QuickJS (default), JavaScriptCore, Hermes, or V8

```mermaid
graph TD
    subgraph "JavaScript Runtime Layer"
        ENGINE[JS Engine]
        LOADER[Module Loader]
        BRIDGE[Native Bridge]
        SCHEDULER[Task Scheduler]
    end

    subgraph "Valdi Core Layer"
        COMPONENTS[Component System]
        RENDERER[Renderer]
        EVENTS[Event System]
        ANIMATIONS[Animation System]
    end

    subgraph "Native Platform Layer"
        VIEWS[Native Views]
        LAYOUT[Layout Engine]
        GRAPHICS[Graphics Context]
        PLATFORM[Platform APIs]
    end

    ENGINE --> LOADER
    LOADER --> BRIDGE
    BRIDGE --> SCHEDULER

    SCHEDULER --> COMPONENTS
    COMPONENTS --> RENDERER
    RENDERER --> EVENTS
    EVENTS --> ANIMATIONS

    ANIMATIONS --> VIEWS
    VIEWS --> LAYOUT
    LAYOUT --> GRAPHICS
    GRAPHICS --> PLATFORM
```

### Memory Management

Valdi employs sophisticated memory management strategies:

- **Component Pooling**: Reuse component instances to reduce allocations
- **View Recycling**: Native view reuse for list components
- **Automatic Cleanup**: Garbage collection coordination between JS and native
- **Weak References**: Prevent memory leaks in event handlers and observers

### Threading Model

```mermaid
graph LR
    subgraph "Main Thread"
        UI[UI Updates]
        EVENTS[Event Handling]
        ANIMATIONS[Animations]
    end

    subgraph "JavaScript Thread"
        COMPONENTS[Component Logic]
        STATE[State Updates]
        BUSINESS[Business Logic]
    end

    subgraph "Background Threads"
        NETWORK[Network Requests]
        DATABASE[Database Operations]
        COMPUTATION[Heavy Computation]
    end

    UI <--> COMPONENTS
    EVENTS <--> STATE
    ANIMATIONS <--> BUSINESS

    BUSINESS --> NETWORK
    BUSINESS --> DATABASE
    BUSINESS --> COMPUTATION
```

## Component Lifecycle

Valdi components follow a well-defined lifecycle that ensures predictable behavior and optimal performance.

### Component Lifecycle Stages

```mermaid
sequenceDiagram
    participant App as Application
    participant Runtime as Valdi Runtime
    participant Component as Component Instance
    participant Renderer as Renderer
    participant Native as Native View

    App->>Runtime: Create Component
    Runtime->>Component: constructor()
    Runtime->>Component: onCreate()
    Component->>Component: Initialize state

    Runtime->>Component: onViewModelUpdate()
    Note over Component: Called if initial ViewModel provided
    Runtime->>Component: onRender()
    Component->>Renderer: Emit JSX
    Renderer->>Native: Create/Update Views

    Note over Component,Native: Component is active

    App->>Runtime: Update ViewModel
    Runtime->>Component: onViewModelUpdate(previousViewModel)
    Runtime->>Component: onRender()
    Component->>Renderer: Emit Updated JSX
    Renderer->>Native: Update Views

    App->>Runtime: Destroy Component
    Runtime->>Component: onDestroy()
    Component->>Component: Cleanup resources
    Runtime->>Native: Remove Views
```

### Component State Management

```mermaid
graph TD
    subgraph "Component State"
        VIEWMODEL[ViewModel]
        INTERNAL[Internal State]
        CONTEXT[Context]
        REFS[Element References]
    end

    subgraph "State Updates"
        PROPS[Property Changes]
        EVENTS[Event Handlers]
        LIFECYCLE[Lifecycle Methods]
        EXTERNAL[External Updates]
    end

    subgraph "Rendering"
        RENDER[onRender Method]
        RECONCILE[Reconciliation]
        UPDATE[View Updates]
    end

    PROPS --> VIEWMODEL
    EVENTS --> INTERNAL
    LIFECYCLE --> CONTEXT
    EXTERNAL --> REFS

    VIEWMODEL --> RENDER
    INTERNAL --> RENDER
    CONTEXT --> RENDER
    REFS --> RENDER

    RENDER --> RECONCILE
    RECONCILE --> UPDATE
```

## Rendering Pipeline

The Valdi rendering pipeline is optimized for performance with minimal overhead and efficient updates.

### Render Cycle

Based on the actual implementation in Renderer.ts, Component.ts, and ViewNodeRenderer.cpp:

```mermaid
sequenceDiagram
    participant StatefulComponent as StatefulComponent
    participant Renderer as Renderer
    participant Component as Component Instance
    participant JSRuntime as JavaScript Runtime
    participant ViewNodeRenderer as ViewNodeRenderer
    participant ViewNode as ViewNode
    participant NativeView as Native View

    StatefulComponent->>StatefulComponent: setState(newState)
    StatefulComponent->>Renderer: scheduleRender()
    Renderer->>Renderer: renderComponent(component)

    Note over Renderer: Check if component needs rendering
    Renderer->>Renderer: doBegin()
    Renderer->>Component: onViewModelUpdate(oldViewModel)
    Note over Component: If viewModel changed
    Renderer->>Component: onRender()
    Component->>Component: renderTemplate.apply()
    Component->>JSRuntime: Emit JSX elements

    JSRuntime->>ViewNodeRenderer: RenderRequest entries
    ViewNodeRenderer->>ViewNodeRenderer: visit(CreateElement)
    ViewNodeRenderer->>ViewNode: Create/Update ViewNode
    ViewNode->>ViewNode: setAttribute()
    ViewNodeRenderer->>ViewNodeRenderer: updateCSS()
    ViewNode->>ViewNode: updateViewTree()
    ViewNode->>NativeView: Apply changes to native view

    Renderer->>Renderer: doEnd()
```

### View Reconciliation

Valdi uses an efficient reconciliation algorithm that minimizes native view operations:

1. **Virtual Tree Comparison**: Compare new and previous virtual trees
2. **Minimal Updates**: Only update changed properties
3. **View Reuse**: Reuse existing native views when possible
4. **Batch Operations**: Group multiple updates for efficiency

### Layout System

```mermaid
graph TD
    subgraph "Layout Input"
        CONSTRAINTS[Layout Constraints]
        STYLES[Style Properties]
        CONTENT[Content Size]
    end

    subgraph "Yoga Layout Engine"
        FLEXBOX[Flexbox Algorithm]
        MEASURE[Measurement]
        POSITION[Positioning]
    end

    subgraph "Layout Output"
        FRAMES[Element Frames]
        POSITIONS[Absolute Positions]
        SIZES[Final Sizes]
    end

    CONSTRAINTS --> FLEXBOX
    STYLES --> FLEXBOX
    CONTENT --> MEASURE

    FLEXBOX --> MEASURE
    MEASURE --> POSITION

    POSITION --> FRAMES
    FRAMES --> POSITIONS
    FRAMES --> SIZES
```

## Data Flow Patterns

Valdi supports multiple data flow patterns to accommodate different application architectures.

### Unidirectional Data Flow

```mermaid
graph TD
    subgraph "Data Sources"
        API[External APIs]
        DATABASE[Local Database]
        STORAGE[Local Storage]
    end

    subgraph "State Management"
        STORE[Data Store]
        OBSERVABLES[RxJS Observables]
        PROVIDERS[Context Providers]
    end

    subgraph "Component Tree"
        ROOT[Root Component]
        CONTAINERS[Container Components]
        PRESENTATIONAL[Presentational Components]
    end

    subgraph "User Interactions"
        EVENTS[User Events]
        GESTURES[Gestures]
        NAVIGATION[Navigation]
    end

    API --> STORE
    DATABASE --> STORE
    STORAGE --> STORE

    STORE --> OBSERVABLES
    OBSERVABLES --> PROVIDERS
    PROVIDERS --> ROOT

    ROOT --> CONTAINERS
    CONTAINERS --> PRESENTATIONAL

    EVENTS --> STORE
    GESTURES --> STORE
    NAVIGATION --> STORE
```

### Bridged Architecture Pattern

For applications that need to integrate with existing native code:

```mermaid
graph TD
    subgraph "Native Layer (iOS/Android)"
        NATIVE_DATA[Native Data Sources]
        NATIVE_SERVICES[Native Services]
        NATIVE_APIS[Platform APIs]
    end

    subgraph "Bridge Layer"
        ANNOTATIONS[Native Annotations]
        CONTEXT[Component Context]
        BINDINGS[Native Bindings]
    end

    subgraph "Valdi Layer"
        COMPONENTS[Valdi Components]
        UI[Cross-Platform UI]
        LOGIC[Business Logic]
    end

    NATIVE_DATA --> ANNOTATIONS
    NATIVE_SERVICES --> CONTEXT
    NATIVE_APIS --> BINDINGS

    ANNOTATIONS --> COMPONENTS
    CONTEXT --> UI
    BINDINGS --> LOGIC
```

### Full-Stack Valdi Pattern

For applications built entirely within the Valdi ecosystem:

```mermaid
graph TD
    subgraph "Data Layer"
        SQL[SQL Database]
        NETWORK[Network Layer]
        CACHE[Caching Layer]
    end

    subgraph "Service Layer"
        WORKERS[Worker Services]
        SYNC[Data Synchronization]
        BUSINESS[Business Logic]
    end

    subgraph "UI Layer"
        REACTIVE[Reactive Components]
        STATE[State Management]
        PRESENTATION[UI Components]
    end

    SQL --> WORKERS
    NETWORK --> SYNC
    CACHE --> BUSINESS

    WORKERS --> REACTIVE
    SYNC --> STATE
    BUSINESS --> PRESENTATION
```

## Performance Architecture

Valdi is designed with performance as a primary concern, implementing multiple optimization strategies.

### Performance Optimization Strategies

1. **Compilation Optimizations**
   - Dead code elimination
   - Tree shaking for unused modules
   - Bytecode compilation for faster execution
   - Native code generation for critical paths

2. **Runtime Optimizations**
   - Component instance pooling
   - Native view recycling
   - Efficient memory management
   - Minimal JavaScript-to-native bridge calls

3. **Rendering Optimizations**
   - Incremental rendering
   - View hierarchy flattening
   - Batch updates
   - Optimized layout calculations

### Performance Monitoring

```mermaid
graph TD
    subgraph "Metrics Collection"
        RENDER_TIME[Render Time]
        MEMORY_USAGE[Memory Usage]
        FRAME_RATE[Frame Rate]
        BRIDGE_CALLS[Bridge Call Count]
    end

    subgraph "Analysis Tools"
        PROFILER[Performance Profiler]
        INSPECTOR[Valdi Inspector]
        DEBUGGER[Native Debugger]
        TRACING[Performance Tracing]
    end

    subgraph "Optimization Actions"
        CODE_SPLITTING[Code Splitting]
        LAZY_LOADING[Lazy Loading]
        CACHING[Intelligent Caching]
        PRELOADING[Resource Preloading]
    end

    RENDER_TIME --> PROFILER
    MEMORY_USAGE --> INSPECTOR
    FRAME_RATE --> DEBUGGER
    BRIDGE_CALLS --> TRACING

    PROFILER --> CODE_SPLITTING
    INSPECTOR --> LAZY_LOADING
    DEBUGGER --> CACHING
    TRACING --> PRELOADING
```

## Integration Points

Valdi provides multiple integration points for connecting with external systems and native platform features.

### Build System Integration

```mermaid
graph TD
    subgraph "Build Systems"
        BAZEL[Bazel]
        XCODE[Xcode]
        GRADLE[Gradle]
        COCOAPODS[CocoaPods]
        SPM[Swift Package Manager]
    end

    subgraph "Valdi Toolchain"
        COMPILER[Valdi Compiler]
        RUNTIME[Valdi Runtime]
        MODULES[Valdi Modules]
    end

    subgraph "Output Artifacts"
        FRAMEWORKS[Native Frameworks]
        LIBRARIES[Static Libraries]
        BUNDLES[Resource Bundles]
        HEADERS[Header Files]
    end

    BAZEL --> COMPILER
    XCODE --> RUNTIME
    GRADLE --> MODULES
    COCOAPODS --> COMPILER
    SPM --> RUNTIME

    COMPILER --> FRAMEWORKS
    RUNTIME --> LIBRARIES
    MODULES --> BUNDLES
    COMPILER --> HEADERS
```

### Native Platform APIs

Valdi provides seamless access to native platform features through multiple mechanisms:

1. **Native Annotations**: Type-safe bindings to native classes and methods
2. **Component Context**: Dependency injection for native services
3. **Custom Views**: Direct integration of native UI components
4. **Platform-Specific Code**: Conditional compilation for platform differences

### Third-Party Integration

```mermaid
graph TD
    subgraph "External Libraries"
        NPM[NPM Packages]
        NATIVE_LIBS[Native Libraries]
        FRAMEWORKS[System Frameworks]
        APIS[External APIs]
    end

    subgraph "Integration Layer"
        BINDINGS[Native Bindings]
        WRAPPERS[TypeScript Wrappers]
        ADAPTERS[Protocol Adapters]
        BRIDGES[Communication Bridges]
    end

    subgraph "Valdi Application"
        COMPONENTS[Valdi Components]
        SERVICES[Application Services]
        MODULES[Feature Modules]
    end

    NPM --> WRAPPERS
    NATIVE_LIBS --> BINDINGS
    FRAMEWORKS --> ADAPTERS
    APIS --> BRIDGES

    WRAPPERS --> COMPONENTS
    BINDINGS --> SERVICES
    ADAPTERS --> MODULES
    BRIDGES --> SERVICES
```

## Extensibility

Valdi is designed to be extensible at multiple levels, allowing developers to customize and extend the framework's capabilities.

### Compiler Extensions

The Valdi compiler supports extensions through:

- **Custom Processors**: Add new compilation steps
- **Code Generators**: Generate platform-specific code
- **Asset Processors**: Handle custom asset types
- **Optimization Passes**: Implement custom optimizations

### Runtime Extensions

```mermaid
graph TD
    subgraph "Extension Points"
        COMPONENTS[Custom Components]
        ELEMENTS[Custom Elements]
        SERVICES[Custom Services]
        PROVIDERS[Custom Providers]
    end

    subgraph "Extension APIs"
        COMPONENT_API[Component API]
        ELEMENT_API[Element API]
        SERVICE_API[Service API]
        PROVIDER_API[Provider API]
    end

    subgraph "Core Runtime"
        RENDERER[Core Renderer]
        LIFECYCLE[Lifecycle Manager]
        BRIDGE[Native Bridge]
        SCHEDULER[Task Scheduler]
    end

    COMPONENTS --> COMPONENT_API
    ELEMENTS --> ELEMENT_API
    SERVICES --> SERVICE_API
    PROVIDERS --> PROVIDER_API

    COMPONENT_API --> RENDERER
    ELEMENT_API --> LIFECYCLE
    SERVICE_API --> BRIDGE
    PROVIDER_API --> SCHEDULER
```

### Plugin Architecture

Valdi supports a plugin architecture for extending functionality:

1. **Compiler Plugins**: Extend compilation capabilities
2. **Runtime Plugins**: Add runtime features
3. **Development Plugins**: Enhance developer experience
4. **Platform Plugins**: Add platform-specific features

### Custom Element Creation

Developers can create custom elements that integrate seamlessly with the Valdi ecosystem:

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant API as Element API
    participant Runtime as Valdi Runtime
    participant Native as Native Platform

    Dev->>API: Define Custom Element
    API->>Runtime: Register Element
    Runtime->>Native: Create Native Implementation

    Note over Dev,Native: Element is available for use

    Dev->>Runtime: Use Custom Element
    Runtime->>Native: Instantiate Native View
    Native->>Runtime: Return View Instance
    Runtime->>Dev: Element Ready
```

## Conclusion

Valdi's architecture is designed to provide a powerful, performant, and flexible foundation for cross-platform mobile development. The modular design allows for easy extension and customization while maintaining consistency and performance across platforms.

Key architectural strengths include:

- **Separation of Concerns**: Clear boundaries between compilation, runtime, and platform layers
- **Performance Optimization**: Multiple levels of optimization from compile-time to runtime
- **Extensibility**: Well-defined extension points for customization
- **Platform Integration**: Seamless integration with native platform features
- **Developer Experience**: Rich tooling and debugging capabilities

This architecture enables Valdi to deliver on its core promise: significantly faster development than traditional native code while maintaining equal or better performance.

## Verification and Limitations

This document was created through analysis of the Valdi codebase. The following aspects have been verified against source code:

**Verified Components:**
- **JavaScript Engine Support**: Confirmed available engines (JavaScriptCore, QuickJS, Hermes, V8) in JavaScriptBridge.cpp
- **Component Lifecycle**: Verified lifecycle methods (onCreate, onRender, onViewModelUpdate, onDestroy) in Component.ts
- **Compilation Modes**: Confirmed js, js_bytecode, and native modes in CompilationMode.swift
- **Module Dependencies**: Verified core module structure from BUILD.bazel and module.yaml files
- **Layout Engine**: Confirmed Yoga (Flexbox) integration in FlexboxLayer.cpp and YogaAttributes.cpp

**Implementation-Based Diagrams:**
- Render cycle sequence diagram traces actual code paths through setState() → scheduleRender() → renderComponent() → onRender() → ViewNodeRenderer
- Component lifecycle reflects exact method calls in Component.ts and Renderer.ts
- JavaScript engine support verified from JavaScriptBridge.cpp implementation
- Module dependencies verified from actual BUILD.bazel files

**Limitations:**
- Performance characteristics are based on code analysis, not runtime measurements
- Some integration patterns may vary depending on specific configurations
- This document reflects the codebase state at the time of analysis and may not reflect future changes

## Related Documentation

- [Getting Started with Valdi](./start-install.md)
- [Component System](./core-component.md)
- [Performance Optimization](./performance-optimization.md)
- [Native Integration](./native-bindings.md)
- [Advanced Full-Stack Architecture](./advanced-full-stack.md)
