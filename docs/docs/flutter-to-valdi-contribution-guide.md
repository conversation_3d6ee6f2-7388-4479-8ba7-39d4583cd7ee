# Flutter to Valdi: Advanced Contribution Guide

This guide focuses on advanced topics for Flutter developers who want to contribute to the Valdi framework itself, including understanding the codebase architecture, performance optimization, and making meaningful contributions.

## Table of Contents

1. [Codebase Architecture Deep Dive](#codebase-architecture-deep-dive)
2. [Performance Analysis and Optimization](#performance-analysis-and-optimization)
3. [Contributing to Core Framework](#contributing-to-core-framework)
4. [Advanced Debugging Techniques](#advanced-debugging-techniques)
5. [Framework Extension Patterns](#framework-extension-patterns)

## Codebase Architecture Deep Dive

### Understanding the Multi-Language Architecture

Valdi's codebase spans multiple languages, each serving specific purposes:

**TypeScript/JavaScript Layer:**
- `src/valdi_modules/src/valdi/valdi_core/` - Core component system
- `src/valdi_modules/src/valdi/valdi_tsx/` - JSX runtime and type definitions
- Component lifecycle, state management, and rendering logic

**Swift Compiler Layer:**
- `compiler/compiler/Compiler/` - TypeScript compilation and optimization
- Module bundling, asset processing, and code generation
- Platform-specific code generation

**C++ Runtime Layer:**
- `composer/src/composer/runtime/` - Core runtime engine
- `valdi_core/src/valdi_core/cpp/` - JavaScript engine integration
- Performance-critical rendering and native bridge

**Platform Integration:**
- `valdi_core/src/valdi_core/ios/` - iOS-specific implementations
- `valdi_core/src/valdi_core/android/` - Android-specific implementations
- Native module factories and platform APIs

### Key Files for Contributors

**Component System (TypeScript):**
```
src/valdi_modules/src/valdi/valdi_core/src/
├── Component.ts              # Base component classes
├── Renderer.ts               # Core rendering engine
├── IRenderer.ts              # Renderer interface
├── JSXBootstrap.ts           # JSX runtime setup
└── utils/
    ├── PartialUtils.ts       # State merging utilities
    └── Callback.ts           # Event handling utilities
```

**Runtime Engine (C++):**
```
composer/src/composer/runtime/
├── JavaScript/
│   ├── JavaScriptRuntime.cpp # JS engine abstraction
│   └── JavaScriptBridge.cpp  # Native-JS communication
├── Renderer/
│   ├── ViewNodeRenderer.cpp  # View tree management
│   └── FlexboxLayer.cpp      # Layout engine
└── Platform/
    ├── ios/                  # iOS-specific runtime
    └── android/              # Android-specific runtime
```

**Compiler (Swift):**
```
compiler/compiler/Compiler/Sources/
├── TypeScript/
│   └── TypeScriptCompiler.swift    # TS compilation
├── Processors/
│   ├── CompileTypeScriptProcessor.swift
│   └── GenerateNativeCodeProcessor.swift
└── CodeGeneration/
    ├── iOSCodeGenerator.swift
    └── AndroidCodeGenerator.swift
```

### Understanding the Compilation Pipeline

**Phase 1: TypeScript Processing**
```swift
// TypeScriptCompiler.swift
func compileModule(module: ValdiModule) -> CompilationResult {
    // 1. Parse TypeScript source files
    let parsedFiles = parseTypeScriptFiles(module.sources)
    
    // 2. Type checking and semantic analysis
    let typeCheckedFiles = performTypeChecking(parsedFiles)
    
    // 3. TSX transformation
    let transformedFiles = transformTSX(typeCheckedFiles)
    
    // 4. Generate JavaScript output
    return generateJavaScript(transformedFiles)
}
```

**Phase 2: Native Code Generation**
```swift
// GenerateNativeCodeProcessor.swift
func generateNativeBindings(module: ValdiModule) -> NativeBindings {
    // 1. Extract native annotations
    let annotations = extractNativeAnnotations(module)
    
    // 2. Generate platform-specific interfaces
    let iosInterfaces = generateiOSInterfaces(annotations)
    let androidInterfaces = generateAndroidInterfaces(annotations)
    
    // 3. Create module factories
    return NativeBindings(ios: iosInterfaces, android: androidInterfaces)
}
```

**Phase 3: Runtime Integration**
```cpp
// JavaScriptRuntime.cpp
void JavaScriptRuntime::loadModule(const std::string& modulePath) {
    // 1. Load compiled JavaScript
    auto jsCode = loadCompiledJavaScript(modulePath);
    
    // 2. Initialize module context
    auto moduleContext = createModuleContext(modulePath);
    
    // 3. Execute module initialization
    executeModuleCode(jsCode, moduleContext);
    
    // 4. Register native bindings
    registerNativeBindings(moduleContext);
}
```

## Performance Analysis and Optimization

### Profiling Valdi Applications

**1. JavaScript Performance Profiling:**
```tsx
// Add performance markers in components
export class PerformanceAwareComponent extends Component<{}> {
  onRender(): void {
    performance.mark('component-render-start');
    
    // Component rendering logic
    this.renderContent();
    
    performance.mark('component-render-end');
    performance.measure(
      'component-render-duration',
      'component-render-start',
      'component-render-end'
    );
  }
}
```

**2. Native Performance Analysis:**
```cpp
// ViewNodeRenderer.cpp - Add timing measurements
void ViewNodeRenderer::updateViewTree() {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // View tree update logic
    performViewTreeUpdate();
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        endTime - startTime
    ).count();
    
    COMPOSER_LOG("ViewTree update took: %lld microseconds", duration);
}
```

**3. Memory Usage Monitoring:**
```tsx
// Memory leak detection utility
export class MemoryMonitor {
  private static componentCount = 0;
  private static activeComponents = new Set<Component<any>>();
  
  static registerComponent(component: Component<any>): void {
    this.componentCount++;
    this.activeComponents.add(component);
    
    if (this.componentCount % 100 === 0) {
      console.log(`Active components: ${this.activeComponents.size}`);
      console.log(`Total created: ${this.componentCount}`);
    }
  }
  
  static unregisterComponent(component: Component<any>): void {
    this.activeComponents.delete(component);
  }
}
```

### Optimization Strategies

**1. Component Rendering Optimization:**
```tsx
// Implement shouldComponentUpdate equivalent
export class OptimizedComponent extends Component<ComplexViewModel> {
  private lastRenderHash?: string;
  
  onViewModelUpdate(previousViewModel?: ComplexViewModel): void {
    const currentHash = this.computeViewModelHash(this.viewModel);
    
    if (currentHash === this.lastRenderHash) {
      // Skip rendering if nothing changed
      return;
    }
    
    this.lastRenderHash = currentHash;
    super.onViewModelUpdate(previousViewModel);
  }
  
  private computeViewModelHash(viewModel: ComplexViewModel): string {
    // Implement efficient hashing for viewModel
    return JSON.stringify(viewModel); // Simplified example
  }
}
```

**2. Native Bridge Optimization:**
```cpp
// Batch native operations to reduce bridge calls
class BatchedNativeOperations {
private:
    std::vector<NativeOperation> pendingOperations;
    std::chrono::steady_clock::time_point lastFlush;
    
public:
    void addOperation(const NativeOperation& operation) {
        pendingOperations.push_back(operation);
        
        // Flush if batch is full or timeout reached
        if (pendingOperations.size() >= BATCH_SIZE || 
            shouldFlushByTimeout()) {
            flush();
        }
    }
    
    void flush() {
        if (!pendingOperations.empty()) {
            executeBatchedOperations(pendingOperations);
            pendingOperations.clear();
            lastFlush = std::chrono::steady_clock::now();
        }
    }
};
```

## Contributing to Core Framework

### Setting Up Development Environment

**1. Build System Setup:**
```bash
# Clone and setup Valdi
<NAME_EMAIL>:Snapchat/Valdi.git
cd Valdi

# Install CLI tools
cd npm_modules/cli/
npm run cli:install

# Setup development environment
valdi dev_setup

# Build core framework
bazel build //valdi_core:valdi_core
```

**2. Running Tests:**
```bash
# Run TypeScript tests
bazel test //src/valdi_modules/src/valdi/valdi_core:test

# Run C++ runtime tests
bazel test //composer:test

# Run iOS integration tests
bazel test //valdi_core:test_ios

# Run Android integration tests
bazel test //valdi_core:test_android
```

### Making Your First Contribution

**1. Component System Enhancement:**

Example: Adding a new lifecycle method to components.

```tsx
// src/valdi_modules/src/valdi/valdi_core/src/Component.ts
export class Component<ViewModel = object, ComponentContext = object> {
  // ... existing code ...
  
  /**
   * Called when the component becomes visible on screen
   * New lifecycle method for performance optimization
   */
  onBecomeVisible(): void {}
  
  /**
   * Called when the component becomes hidden
   * Useful for pausing animations or subscriptions
   */
  onBecomeHidden(): void {}
}
```

**2. Renderer Enhancement:**

```tsx
// src/valdi_modules/src/valdi/valdi_core/src/Renderer.ts
export class Renderer implements IRenderer {
  // ... existing code ...
  
  private notifyVisibilityChange(component: IComponent, isVisible: boolean): void {
    if (isVisible && typeof component.onBecomeVisible === 'function') {
      component.onBecomeVisible();
    } else if (!isVisible && typeof component.onBecomeHidden === 'function') {
      component.onBecomeHidden();
    }
  }
}
```

**3. Native Runtime Enhancement:**

```cpp
// composer/src/composer/runtime/Renderer/ViewNodeRenderer.cpp
void ViewNodeRenderer::updateViewVisibility(ViewNode* node, bool isVisible) {
    // Update native view visibility
    updateNativeViewVisibility(node, isVisible);
    
    // Notify JavaScript layer about visibility change
    if (auto component = node->getAssociatedComponent()) {
        notifyComponentVisibilityChange(component, isVisible);
    }
}
```

### Testing Your Contributions

**1. Unit Tests for Components:**
```tsx
// src/valdi_modules/src/valdi/valdi_core/test/ComponentLifecycleTest.ts
import { TestRenderer } from 'valdi_test';
import { Component } from '../src/Component';

describe('Component Lifecycle', () => {
  it('should call onBecomeVisible when component becomes visible', () => {
    class TestComponent extends Component<{}> {
      onBecomeVisibleCalled = false;
      
      onBecomeVisible(): void {
        this.onBecomeVisibleCalled = true;
      }
      
      onRender(): void {
        <view />;
      }
    }
    
    const renderer = new TestRenderer();
    const component = renderer.render(TestComponent, {});
    
    // Simulate visibility change
    renderer.setComponentVisibility(component, true);
    
    expect(component.onBecomeVisibleCalled).toBe(true);
  });
});
```

**2. Integration Tests:**
```tsx
// apps/test_app/src/valdi/lifecycle_test/src/LifecycleTestApp.tsx
export class LifecycleTestApp extends StatefulComponent<{}, { visibilityEvents: string[] }> {
  state = { visibilityEvents: [] };
  
  onBecomeVisible(): void {
    this.setState({
      visibilityEvents: [...this.state.visibilityEvents, 'visible']
    });
  }
  
  onBecomeHidden(): void {
    this.setState({
      visibilityEvents: [...this.state.visibilityEvents, 'hidden']
    });
  }
  
  onRender(): void {
    <layout direction="column">
      <label value={`Events: ${this.state.visibilityEvents.join(', ')}`} />
    </layout>;
  }
}
```

### Code Review Guidelines

**1. Performance Considerations:**
- Minimize JavaScript-to-native bridge calls
- Use efficient data structures
- Implement proper cleanup in lifecycle methods
- Consider memory usage implications

**2. API Design:**
- Follow existing naming conventions
- Maintain backward compatibility
- Provide comprehensive documentation
- Include usage examples

**3. Testing Requirements:**
- Unit tests for new functionality
- Integration tests for complex features
- Performance benchmarks for optimizations
- Cross-platform compatibility tests

## Advanced Debugging Techniques

### JavaScript Runtime Debugging

**1. Custom Logging System:**
```tsx
// Enhanced logging for development
export class DebugLogger {
  private static isEnabled = __DEV__;
  
  static logComponentRender(component: Component<any>, duration: number): void {
    if (this.isEnabled) {
      console.log(`[RENDER] ${component.constructor.name}: ${duration}ms`);
    }
  }
  
  static logStateChange(component: Component<any>, oldState: any, newState: any): void {
    if (this.isEnabled) {
      console.log(`[STATE] ${component.constructor.name}:`, {
        old: oldState,
        new: newState,
        diff: this.computeStateDiff(oldState, newState)
      });
    }
  }
}
```

**2. Runtime Inspection:**
```tsx
// Runtime component tree inspection
export class ComponentInspector {
  static inspectComponentTree(rootComponent: Component<any>): ComponentTreeNode {
    return {
      component: rootComponent,
      type: rootComponent.constructor.name,
      state: rootComponent.state,
      viewModel: rootComponent.viewModel,
      children: this.getComponentChildren(rootComponent).map(child =>
        this.inspectComponentTree(child)
      )
    };
  }
  
  static findComponentsByType<T extends Component<any>>(
    root: Component<any>,
    componentType: new (...args: any[]) => T
  ): T[] {
    const results: T[] = [];
    
    if (root instanceof componentType) {
      results.push(root);
    }
    
    this.getComponentChildren(root).forEach(child => {
      results.push(...this.findComponentsByType(child, componentType));
    });
    
    return results;
  }
}
```

### Native Runtime Debugging

**1. C++ Debug Utilities:**
```cpp
// composer/src/composer/runtime/Debug/DebugUtils.cpp
class DebugUtils {
public:
    static void logViewHierarchy(ViewNode* root, int depth = 0) {
        std::string indent(depth * 2, ' ');
        COMPOSER_LOG("%s%s (id: %d)", 
                    indent.c_str(), 
                    root->getTypeName().c_str(), 
                    root->getId());
        
        for (auto child : root->getChildren()) {
            logViewHierarchy(child, depth + 1);
        }
    }
    
    static void validateViewTree(ViewNode* root) {
        // Validate view tree consistency
        if (!root->isValid()) {
            COMPOSER_ERROR("Invalid view node detected: %d", root->getId());
        }
        
        for (auto child : root->getChildren()) {
            validateViewTree(child);
        }
    }
};
```

## Framework Extension Patterns

### Creating Custom Elements

**1. Define Element Interface:**
```tsx
// src/custom_elements/src/CustomButton.ts
export interface CustomButtonAttributes {
  title: string;
  variant: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  onPress?: () => void;
}

export interface CustomButtonElement extends IRenderedElement {
  setTitle(title: string): void;
  setVariant(variant: string): void;
  setDisabled(disabled: boolean): void;
}
```

**2. Implement Native Element (iOS):**
```objc
// src/custom_elements/src/ios/CustomButtonView.m
@interface CustomButtonView : UIButton
@property (nonatomic, copy) void (^onPressCallback)(void);
@end

@implementation CustomButtonView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self addTarget:self action:@selector(buttonPressed) forControlEvents:UIControlEventTouchUpInside];
    }
    return self;
}

- (void)buttonPressed {
    if (self.onPressCallback) {
        self.onPressCallback();
    }
}

- (void)setVariant:(NSString *)variant {
    if ([variant isEqualToString:@"primary"]) {
        self.backgroundColor = [UIColor systemBlueColor];
    } else if ([variant isEqualToString:@"danger"]) {
        self.backgroundColor = [UIColor systemRedColor];
    } else {
        self.backgroundColor = [UIColor systemGrayColor];
    }
}

@end
```

**3. Register Element with Runtime:**
```cpp
// src/custom_elements/src/CustomButtonRenderer.cpp
class CustomButtonRenderer : public ElementRenderer {
public:
    std::unique_ptr<NativeView> createNativeView() override {
        return std::make_unique<CustomButtonNativeView>();
    }

    void updateAttributes(NativeView* view, const AttributeMap& attributes) override {
        auto buttonView = static_cast<CustomButtonNativeView*>(view);

        if (auto title = attributes.getString("title")) {
            buttonView->setTitle(*title);
        }

        if (auto variant = attributes.getString("variant")) {
            buttonView->setVariant(*variant);
        }

        if (auto disabled = attributes.getBool("disabled")) {
            buttonView->setDisabled(*disabled);
        }
    }
};

// Register the custom element
REGISTER_ELEMENT_RENDERER("custom-button", CustomButtonRenderer);
```

### Creating Compiler Plugins

**1. TypeScript Transformation Plugin:**
```swift
// compiler/plugins/CustomElementPlugin.swift
class CustomElementPlugin: CompilerPlugin {
    func processTypeScriptFile(_ file: TypeScriptFile) -> TypeScriptFile {
        // Transform custom JSX elements
        let transformer = CustomElementTransformer()
        return transformer.transform(file)
    }
}

class CustomElementTransformer {
    func transform(_ file: TypeScriptFile) -> TypeScriptFile {
        // Find custom-button elements and add type checking
        let visitor = CustomElementVisitor()
        return visitor.visit(file)
    }
}
```

**2. Code Generation Plugin:**
```swift
// compiler/plugins/NativeCodeGenerator.swift
class CustomElementCodeGenerator: CodeGenerator {
    func generateiOSCode(for element: CustomElement) -> String {
        return """
        @interface \(element.name)View : UIView
        \(generateProperties(for: element))
        @end

        @implementation \(element.name)View
        \(generateImplementation(for: element))
        @end
        """
    }

    func generateAndroidCode(for element: CustomElement) -> String {
        return """
        public class \(element.name)View extends View {
            \(generateProperties(for: element))
            \(generateImplementation(for: element))
        }
        """
    }
}
```

### Performance Monitoring Extensions

**1. Custom Performance Metrics:**
```tsx
// src/performance/src/PerformanceMonitor.ts
export class PerformanceMonitor {
  private static metrics = new Map<string, PerformanceMetric>();

  static startMeasurement(name: string): PerformanceMeasurement {
    const start = performance.now();
    return {
      end: () => {
        const duration = performance.now() - start;
        this.recordMetric(name, duration);
        return duration;
      }
    };
  }

  static recordMetric(name: string, value: number): void {
    const metric = this.metrics.get(name) || new PerformanceMetric(name);
    metric.addSample(value);
    this.metrics.set(name, metric);
  }

  static getMetrics(): PerformanceReport {
    const report: PerformanceReport = {};

    this.metrics.forEach((metric, name) => {
      report[name] = {
        average: metric.getAverage(),
        min: metric.getMin(),
        max: metric.getMax(),
        count: metric.getSampleCount()
      };
    });

    return report;
  }
}
```

**2. Component Performance Decorator:**
```tsx
// src/performance/src/PerformanceDecorator.ts
export function withPerformanceMonitoring<T extends Component<any>>(
  ComponentClass: ComponentConstructor<T>
): ComponentConstructor<T> {
  return class extends ComponentClass {
    onRender(): void {
      const measurement = PerformanceMonitor.startMeasurement(
        `${this.constructor.name}.render`
      );

      super.onRender();

      const duration = measurement.end();

      if (duration > SLOW_RENDER_THRESHOLD) {
        console.warn(`Slow render detected: ${this.constructor.name} took ${duration}ms`);
      }
    }

    setState(state: any): void {
      const measurement = PerformanceMonitor.startMeasurement(
        `${this.constructor.name}.setState`
      );

      super.setState(state);

      measurement.end();
    }
  };
}

// Usage
@withPerformanceMonitoring
export class MyComponent extends StatefulComponent<{}, {}> {
  // Component implementation
}
```

### Developer Tools Extensions

**1. Component Inspector Plugin:**
```tsx
// src/dev_tools/src/ComponentInspector.ts
export class ComponentInspectorPlugin {
  private selectedComponent?: Component<any>;

  inspectComponent(component: Component<any>): ComponentInspectionData {
    return {
      type: component.constructor.name,
      state: this.serializeState(component.state),
      viewModel: this.serializeViewModel(component.viewModel),
      context: this.serializeContext(component.context),
      children: this.getComponentChildren(component),
      performance: this.getPerformanceData(component)
    };
  }

  modifyComponentState(component: Component<any>, newState: any): void {
    if (component instanceof StatefulComponent) {
      component.setState(newState);
    }
  }

  highlightComponent(component: Component<any>): void {
    const elements = component.renderer.getComponentRootElements(component, false);
    elements.forEach(element => {
      this.addHighlightOverlay(element);
    });
  }
}
```

**2. Hot Reload Enhancement:**
```tsx
// src/dev_tools/src/HotReloadManager.ts
export class HotReloadManager {
  private componentRegistry = new Map<string, ComponentConstructor<any>>();

  registerComponent(name: string, constructor: ComponentConstructor<any>): void {
    this.componentRegistry.set(name, constructor);
  }

  updateComponent(name: string, newConstructor: ComponentConstructor<any>): void {
    const oldConstructor = this.componentRegistry.get(name);

    if (oldConstructor) {
      this.replaceComponentInstances(oldConstructor, newConstructor);
      this.componentRegistry.set(name, newConstructor);
    }
  }

  private replaceComponentInstances(
    oldConstructor: ComponentConstructor<any>,
    newConstructor: ComponentConstructor<any>
  ): void {
    // Find all instances of the old component
    const instances = this.findComponentInstances(oldConstructor);

    instances.forEach(instance => {
      // Preserve state and context
      const state = instance.state;
      const viewModel = instance.viewModel;
      const context = instance.context;

      // Create new instance
      const newInstance = new newConstructor(instance.renderer, viewModel, context);

      if (newInstance instanceof StatefulComponent && state) {
        newInstance.setState(state);
      }

      // Replace in component tree
      this.replaceComponentInTree(instance, newInstance);
    });
  }
}
```

### Testing Framework Extensions

**1. Custom Test Utilities:**
```tsx
// src/testing/src/TestUtils.ts
export class ValdiTestUtils {
  static async waitForComponentUpdate(
    component: Component<any>,
    timeout = 5000
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Component update timeout'));
      }, timeout);

      const observer = {
        onComponentWillRerender: (updatedComponent: Component<any>) => {
          if (updatedComponent === component) {
            clearTimeout(timeoutId);
            component.renderer.removeObserver(observer);
            resolve();
          }
        }
      };

      component.renderer.addObserver(observer);
    });
  }

  static simulateUserInteraction(
    element: IRenderedElement,
    interaction: UserInteraction
  ): void {
    switch (interaction.type) {
      case 'tap':
        this.simulateTap(element, interaction.position);
        break;
      case 'swipe':
        this.simulateSwipe(element, interaction.direction, interaction.distance);
        break;
      case 'pinch':
        this.simulatePinch(element, interaction.scale);
        break;
    }
  }
}
```

**2. Performance Testing:**
```tsx
// src/testing/src/PerformanceTest.ts
export class PerformanceTestSuite {
  static async measureRenderPerformance(
    ComponentClass: ComponentConstructor<any>,
    viewModel: any,
    iterations = 100
  ): Promise<PerformanceResults> {
    const measurements: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const renderer = new TestRenderer();
      const start = performance.now();

      renderer.render(ComponentClass, viewModel);

      const end = performance.now();
      measurements.push(end - start);

      renderer.dispose();
    }

    return {
      average: measurements.reduce((a, b) => a + b) / measurements.length,
      min: Math.min(...measurements),
      max: Math.max(...measurements),
      p95: this.calculatePercentile(measurements, 95),
      p99: this.calculatePercentile(measurements, 99)
    };
  }

  static async measureMemoryUsage(
    testFunction: () => Promise<void>
  ): Promise<MemoryUsageReport> {
    const initialMemory = this.getCurrentMemoryUsage();

    await testFunction();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    const finalMemory = this.getCurrentMemoryUsage();

    return {
      initial: initialMemory,
      final: finalMemory,
      delta: finalMemory - initialMemory
    };
  }
}
```

This guide provides the foundation for advanced Valdi development and contribution. Focus on understanding the multi-layered architecture, performance implications, and testing strategies as you work on framework improvements.

## Summary

This comprehensive guide covers:

1. **Deep Architecture Understanding** - Multi-language codebase navigation
2. **Performance Analysis** - Profiling and optimization techniques
3. **Core Contributions** - Framework enhancement patterns
4. **Advanced Debugging** - Runtime inspection and debugging tools
5. **Framework Extensions** - Custom elements, plugins, and developer tools

Start with understanding the existing codebase, then gradually work on small improvements before tackling major features. The Valdi community welcomes contributions that improve performance, developer experience, and framework capabilities.
